import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';

import { KeycloakService } from 'keycloak-angular';
import { Observable, catchError, concatAll, forkJoin, from, map, switchMap, throwError } from 'rxjs';

import { EnvironmentService } from '@/app/core/environment/environment.service';
import { TokenRefreshService } from '@/app/core/services/token-refresh.service';

@Injectable()
export class ApiInterceptor implements HttpInterceptor {
  constructor(
    private readonly keycloak: KeycloakService,
    private readonly environment: EnvironmentService,
    private readonly tokenRefreshService: TokenRefreshService
  ) {}

  public intercept(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const requestUrl = req.url;

    if (requestUrl.indexOf('api/') !== -1) {
      return this.modifyBackendRequest$(req, next);
    }

    return next.handle(req);
  }

  private modifyBackendRequest$(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    const token$ = from(this.keycloak.getToken());

    return forkJoin([token$, this.environment.apiUrl$]).pipe(
      map(([token, apiUrl]) => {
        let requestUrl = req.url;
        requestUrl = requestUrl.replace('@api', apiUrl);

        const backendRequest = req.clone({
          url: requestUrl,
          setHeaders: {
            Authorization: `Bearer ${token}`,
          },
        });

        return next.handle(backendRequest);
      }),
      concatAll(),
      catchError((error: unknown) => {
        if (error instanceof HttpErrorResponse) {
          // Handle 401 Unauthorized responses by attempting token refresh
          if (error.status === 401 && this.keycloak.isLoggedIn()) {
            return this.handleUnauthorizedError$(req, next);
          }
        }
        return throwError(() => error);
      })
    );
  }

  private handleUnauthorizedError$(req: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
    return from(this.tokenRefreshService.refreshToken()).pipe(
      switchMap((refreshSuccess) => {
        if (refreshSuccess) {
          // Retry the original request with the new token
          return this.modifyBackendRequest$(req, next);
        } else {
          // Token refresh failed, let the error propagate
          return throwError(
            () =>
              new HttpErrorResponse({
                status: 401,
                statusText: 'Unauthorized - Token refresh failed',
              })
          );
        }
      }),
      catchError((error: unknown) => {
        // If token refresh fails, propagate the original error
        return throwError(() => error);
      })
    );
  }
}
