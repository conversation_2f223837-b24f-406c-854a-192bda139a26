import { Injectable, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';

import { KeycloakService } from 'keycloak-angular';
import { BehaviorSubject, Subject } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class TokenRefreshService implements OnDestroy {
  private readonly destroy$ = new Subject<void>();

  private readonly refreshInProgress$ = new BehaviorSubject<boolean>(false);

  private refreshTimer?: ReturnType<typeof setTimeout>;

  constructor(
    private readonly keycloak: KeycloakService,
    private readonly router: Router
  ) {}

  /**
   * Starts the token refresh mechanism
   * This should be called after successful Keycloak initialization
   */
  public startTokenRefresh(): void {
    if (!this.keycloak.isLoggedIn()) {
      return;
    }

    this.scheduleNextRefresh();
  }

  /**
   * Stops the token refresh mechanism
   */
  public stopTokenRefresh(): void {
    if (this.refreshTimer) {
      clearTimeout(this.refreshTimer);
      this.refreshTimer = undefined;
    }
  }

  /**
   * Manually refresh the token
   * Returns a promise that resolves to true if refresh was successful
   */
  public async refreshToken(): Promise<boolean> {
    if (!this.keycloak.isLoggedIn()) {
      console.warn('User is not logged in, cannot refresh token');
      return false;
    }

    if (this.refreshInProgress$.value) {
      // Wait for ongoing refresh to complete
      return new Promise((resolve) => {
        const subscription = this.refreshInProgress$.subscribe((inProgress) => {
          if (!inProgress) {
            subscription.unsubscribe();
            resolve(this.keycloak.isLoggedIn());
          }
        });
      });
    }

    this.refreshInProgress$.next(true);

    try {
      const refreshed = await this.keycloak.updateToken(30); // Refresh if token expires within 30 seconds

      if (refreshed) {
        this.scheduleNextRefresh();
        return true;
      } else {
        this.scheduleNextRefresh();
        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
      this.handleRefreshError();
      return false;
    } finally {
      this.refreshInProgress$.next(false);
    }
  }

  /**
   * Get the time until token expiration in milliseconds
   */
  private getTimeUntilExpiration(): number {
    const token = this.keycloak.getKeycloakInstance().tokenParsed;
    if (!token?.exp) {
      return 0;
    }

    const expirationTime = token.exp * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeUntilExpiration = expirationTime - currentTime;

    return Math.max(0, timeUntilExpiration);
  }

  /**
   * Schedule the next token refresh
   * Refreshes the token 5 minutes before it expires, or immediately if it expires sooner
   */
  private scheduleNextRefresh(): void {
    this.stopTokenRefresh(); // Clear any existing timer

    const timeUntilExpiration = this.getTimeUntilExpiration();

    if (timeUntilExpiration <= 0) {
      // Token is already expired, refresh immediately
      this.refreshToken();
      return;
    }

    // Schedule refresh 5 minutes (300000ms) before expiration, but at least 30 seconds from now
    const refreshBuffer = 5 * 60 * 1000; // 5 minutes in milliseconds
    const minDelay = 30 * 1000; // 30 seconds minimum delay
    const refreshDelay = Math.max(minDelay, timeUntilExpiration - refreshBuffer);

    console.log(`Next token refresh scheduled in ${Math.round(refreshDelay / 1000)} seconds`);

    this.refreshTimer = setTimeout(() => {
      this.refreshToken();
    }, refreshDelay);
  }

  /**
   * Handle token refresh errors
   */
  public handleRefreshError(): void {
    console.error('Token refresh failed, redirecting to login');
    this.stopTokenRefresh();

    // Navigate to error page or login
    this.router.navigate(['error-timeout']).catch((err) => {
      console.error('Navigation error:', err);
    });
  }

  public ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.stopTokenRefresh();
  }
}
