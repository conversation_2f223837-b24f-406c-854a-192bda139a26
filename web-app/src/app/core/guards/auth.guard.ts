import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Router, RouterStateSnapshot, UrlTree } from '@angular/router';

import { KeycloakAuthGuard, KeycloakService } from 'keycloak-angular';

@Injectable({
  providedIn: 'root',
})
export class AuthGuard extends KeycloakAuthGuard {
  constructor(
    protected override readonly router: Router,
    protected readonly keycloak: KeycloakService
  ) {
    super(router, keycloak);
  }

  public override async isAccessAllowed(_route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Promise<boolean | UrlTree> {
    if (state.url.toLowerCase().trim() === '/login') {
      if (this.authenticated) {
        const redirectUrl = _route.queryParams['redirectUrl'];
        if (redirectUrl && redirectUrl !== '/login' && redirectUrl !== '/') {
          return !(await this.router.navigate([redirectUrl]));
        }
        return !(await this.router.navigate(['/']));
      }

      return true;
    }

    if (state.url.toLowerCase().trim() === '/keycloak/login') {
      if (!this.authenticated) {
        this.keycloak?.login({
          redirectUri: window.location.href,
        });

        return false;
      } else {
        const redirectUrl = _route.queryParams['redirectUrl'];
        if (redirectUrl && redirectUrl !== '/login' && redirectUrl !== '/') {
          return await this.router.navigate([redirectUrl]);
        }
        return await this.router.navigate(['/']);
      }
    }

    if (!this.keycloak.isLoggedIn()) {
      return !(await this.router.navigate(['/login'], {
        queryParams: { redirectUrl: state.url },
      }));
    }

    return true;
  }
}
