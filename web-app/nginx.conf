# Expires map
map $sent_http_content_type $expires {
  default off;
  text/html epoch;
  text/css max;
  application/json max;
  application/javascript max;
  ~image/ max;
}

server {
  listen 8080;
  location / {
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'nonce-$request_id'; style-src 'self' 'unsafe-inline'; font-src 'self'; img-src 'self' blob: data:; connect-src 'self' ${KEYCLOAK_URL} ${API_URL};";
    # Override CSP for PDFs so that the native browser PDF viewer does not break
    if ($sent_http_content_type ~ "application/pdf") {
      add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' blob:; style-src 'self' 'unsafe-inline' blob:; object-src 'self' blob: 'unsafe-inline';" always;
    }
    add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    add_header X-Content-Type-Options "nosniff";
    add_header Cache-Control "max-age=300, private";

    sub_filter_once off;
    sub_filter randomNonceGoesHere 'nonce-$request_id';

    root /usr/share/nginx/html;
    index index.html;

    try_files $uri $uri/ /index.html =404;
  }
  expires $expires;
  gzip on;

  access_log /dev/null;
  error_log /dev/stderr;
}
