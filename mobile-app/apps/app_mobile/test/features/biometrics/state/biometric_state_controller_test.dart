import 'package:app_mobile/dependency_injection/repositories.dart';
import 'package:app_mobile/features/biometrics/domain/biometric_repository.dart';
import 'package:app_mobile/features/biometrics/providers/biometric_provider.dart';
import 'package:app_mobile/features/biometrics/state/biometric_controller.dart';
import 'package:app_mobile/features/biometrics/state/biometric_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../mock/mock_repositories.mocks.dart';
import '../../settings/domain/settings_repository_test.dart';

@GenerateMocks([BiometricRepository, SettingsRepository])
void main() {
  late ProviderContainer container;
  late MockBiometricRepository biometricRepository;
  late MockSettingsRepository settingsRepository;

  setUp(() {
    biometricRepository = MockBiometricRepository();
    settingsRepository = MockSettingsRepository();
    container = ProviderContainer(
      overrides: [
        biometricRepositoryProvider.overrideWithValue(biometricRepository),
        settingsRepositoryProvider.overrideWithValue(settingsRepository),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  testWidgets('checkBiometrics sets state when biometric is not available',
      (WidgetTester tester) async {
    final controller = container.read(biometricControllerProvider.notifier);

    when(biometricRepository.isBiometricAvailable())
        .thenAnswer((_) async => false);

    await tester.pumpWidget(
      ProviderScope(
        overrides: [
          biometricRepositoryProvider.overrideWithValue(biometricRepository),
          settingsRepositoryProvider.overrideWithValue(settingsRepository),
        ],
        child: MaterialApp(
          home: Builder(
            builder: (BuildContext context) {
              return Container();
            },
          ),
        ),
      ),
    );

    final BuildContext context = tester.element(find.byType(Container));

    await controller.checkBiometrics(context);

    expect(
      container.read(biometricControllerProvider),
      AsyncValue.data(
        BiometricState(
          isBiometricAvailable: false,
          useBiometrics: null,
        ),
      ),
    );
  });
}
